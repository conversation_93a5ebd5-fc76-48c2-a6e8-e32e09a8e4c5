
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import ProtectedRoute from "@/components/ProtectedRoute";
import Layout from "@/components/Layout";
import WalletManagement from "@/components/settings/WalletManagement";
import CategoryManagement from "@/components/settings/CategoryManagement";
import UserSettingsManagement from "@/components/settings/UserSettingsManagement";

const Settings = () => {
  return (
    <ProtectedRoute>
      <Layout>
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Pengaturan</CardTitle>
            <CardDescription>
              <PERSON><PERSON>la mata uang, dompet, dan kategori untuk aplikasi keuangan Anda
            </CardDescription>
          </CardHeader>
        </Card>

        <Tabs defaultValue="general" className="w-full">
          <TabsList className="grid w-full grid-cols-3 mb-3">
            <TabsTrigger value="general" className="text-xs sm:text-sm">Umum</TabsTrigger>
            <TabsTrigger value="wallets" className="text-xs sm:text-sm">Dompet</TabsTrigger>
            <TabsTrigger value="categories" className="text-xs sm:text-sm">Kategori</TabsTrigger>
          </TabsList>

          <TabsContent value="general">
            <UserSettingsManagement />
          </TabsContent>

          <TabsContent value="wallets">
            <WalletManagement />
          </TabsContent>

          <TabsContent value="categories">
            <CategoryManagement />
          </TabsContent>
        </Tabs>
      </Layout>
    </ProtectedRoute>
  );
};

export default Settings;
